import {Path} from '@panda-design/path-form';
import {Form, FormRule} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import {useCallback, useEffect, useMemo, useRef} from 'react';
import {MCPServerAuthType} from '@/types/mcp/mcp';
import {setSystemUpdating} from '../../../MCPEdit/hooks';

interface FormConfig {
    isRequired: boolean;
    placeholder: string;
    rules: FormRule[];
}

interface FieldPaths {
    authDescription: Path;
    userModifiedContent: Path;
    userHasModified: Path;
}

interface UseAuthDescriptionReturn {
    authType: MCPServerAuthType;
    fieldPaths: FieldPaths;
    formConfig: FormConfig;
    handleChange: (value: string) => void;
}

const CLOUD_IAM_DEFAULT_CONTENT = '根据AccessKey SecretKey进行认证，获取方式参考 云上百度（百度云度厂版）相关文档 https://cloud.baidu-int.com/icloud/help/%E4%BC%81%E4%B8%9A%E7%BB%84%E7%BB%87/%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/';

export const useAuthDescription = (path: Path = []): UseAuthDescriptionReturn => {
    const form = Form.useFormInstance();

    const serverExtensionPath = useMemo(
        () => [...path, 'serverConf', 'serverExtension'],
        [path]
    );

    const authType = useWatch([...serverExtensionPath, 'serverAuthType'], form) as MCPServerAuthType;
    const authDescription = useWatch([...serverExtensionPath, 'authDescription'], form);
    // 用户修改（隐藏）
    const userModifiedContent = useWatch([...serverExtensionPath, '_userModifiedContent'], form);
    // 用户是否修改过（隐藏）
    const userHasModified = useWatch([...serverExtensionPath, '_userHasModified'], form);

    const isInitializedRef = useRef(false);

    const fieldPaths = useMemo(
        () => ({
            authDescription: [...serverExtensionPath, 'authDescription'],
            userModifiedContent: [...serverExtensionPath, '_userModifiedContent'],
            userHasModified: [...serverExtensionPath, '_userHasModified'],
        }),
        [serverExtensionPath]
    );

    const isValidAuthDescription = useCallback(
        (description: string) => {
            return description !== '' && description !== null && description !== CLOUD_IAM_DEFAULT_CONTENT;
        },
        []
    );

    const getNewAuthValue = useCallback(
        (authType: MCPServerAuthType, hasUserModified: boolean, userContent: string): string => {
            if (authType === 'CLOUD_INIT_IAM') {
                // 用户是否修改且内容非空 是-> 显示用户输入内容 否-> 显示默认内容
                const hasNonEmptyUserContent = userContent && userContent.trim() !== '';
                return hasUserModified && hasNonEmptyUserContent ? userContent : CLOUD_IAM_DEFAULT_CONTENT;
            }

            if (authType === 'OTHER') {
                // 用户是否修改 是-> 显示用户输入内容或空字符串 否-> 显示默认内容
                return hasUserModified ? userContent || '' : '';
            }
            return '';
        },
        []
    );

    const updateFormFields = useCallback(
        (updates: Record<string, string | boolean>) => {
            setSystemUpdating(true);
            try {
                Object.entries(updates).forEach(([fieldPath, value]) => {
                    const pathArray = fieldPath.split('.');
                    form.setFieldValue(pathArray, value);
                });
            } finally {
                setSystemUpdating(false);
            }
        },
        [form]
    );

    useEffect(
        () => {
            if (!isInitializedRef.current && authDescription !== undefined) {
                if (isValidAuthDescription(authDescription)) {
                    updateFormFields({
                        [fieldPaths.userModifiedContent.join('.')]: authDescription,
                        [fieldPaths.userHasModified.join('.')]: true,
                    });
                }
                isInitializedRef.current = true;
            }
        },
        [authDescription, isValidAuthDescription, updateFormFields, fieldPaths]
    );

    useEffect(
        () => {
            if (!isInitializedRef.current || authType === 'NONE') {
                return;
            }

            const hasUserModified = userHasModified === true;
            const newValue = getNewAuthValue(authType, hasUserModified, userModifiedContent);

            updateFormFields({
                [fieldPaths.authDescription.join('.')]: newValue,
            });
        },
        [authType, userModifiedContent, userHasModified, getNewAuthValue, updateFormFields, fieldPaths]
    );
    // 每次输入都需同步到 _userModifiedContent，并标记 _userHasModified = true，保证后续逻辑能正确区分“默认值”和“用户输入”。
    const handleChange = useCallback(
        (value: string) => {
            updateFormFields({
                [fieldPaths.userModifiedContent.join('.')]: value,
                [fieldPaths.userHasModified.join('.')]: true,
            });
        },
        [updateFormFields, fieldPaths]
    );

    const formConfig = useMemo(
        () => {
            const isRequired = authType === 'CLOUD_INIT_IAM' || authType === 'OTHER';

            return {
                isRequired,
                placeholder: '请说明鉴权的方法以及获取鉴权凭证的方式',
                rules: isRequired ? [{required: true, message: '请输入鉴权方法'}] : [],
            };
        },
        [authType]
    );

    return {
        authType,
        fieldPaths,
        formConfig,
        handleChange,
    };
};
